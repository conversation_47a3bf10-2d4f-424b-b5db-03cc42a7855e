{"name": "Weather Bordj - <PERSON><PERSON><PERSON>", "nodes": [{"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilio", "position": [1030, 400], "parameters": {"resource": "message", "operation": "send", "from": "whatsapp:+14155238886", "to": "whatsapp:+213XXXXXXXXX", "toWhatsApp": true, "message": "=🌤️ *<PERSON><PERSON><PERSON><PERSON>j*\n\n🌡️ *Température:* {{$node[\"OpenWeatherMap\"].json[\"main\"][\"temp\"]}}°C\n💨 *Ressenti:* {{$node[\"OpenWeatherMap\"].json[\"main\"][\"feels_like\"]}}°C\n💧 *Humidité:* {{$node[\"OpenWeatherMap\"].json[\"main\"][\"humidity\"]}}%\n☁️ *Temps:* {{$node[\"OpenWeatherMap\"].json[\"weather\"][0][\"description\"]}}\n\n📅 {{new Date().toLocaleDateString('fr-FR', {weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'})}}\n⏰ {{new Date().toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})}}"}, "typeVersion": 1}, {"name": "OpenWeatherMap", "type": "n8n-nodes-base.openWeatherMap", "position": [830, 400], "parameters": {"cityName": "Bordj <PERSON>,DZ", "format": "json", "units": "metric"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [630, 400], "parameters": {"triggerTimes": {"item": [{"hour": 8, "minute": 0}]}}, "typeVersion": 1}], "connections": {"Cron": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}, "OpenWeatherMap": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}