{"\"id\"": "\"wokWVLDQUDi0DC7I\",", "\"meta\"": "{", "\"instanceId\"": "\"03907a25f048377a8789a4332f28148522ba31ee907fababf704f1d88130b1b6\",", "\"templateCredsSetupCompleted\"": "true", "\"name\"": "\"Perplexity\"", "\"tags\"": "[],", "\"nodes\"": "[", "\"type\"": "\"ai_tool\",", "\"position\"": "[", "\"parameters\"": "{", "\"color\"": "3,", "\"width\"": "420,", "\"height\"": "340,", "\"content\"": "\"## Optional\"", "\"typeVersion\"": "4.2", "\"model\"": "\"gpt-4o-mini-2024-07-18\",", "\"options\"": "{},", "\"responseFormat\"": "\"text\",", "\"credentials\"": "{", "\"openAiApi\"": "{", "\"topP\"": "1,", "\"timeout\"": "60000,", "\"maxTokens\"": "-1,", "\"maxRetries\"": "2,", "\"temperature\"": "0,", "\"presencePenalty\"": "0,", "\"frequencyPenalty\"": "0", "\"schemaType\"": "\"manual\",", "\"inputSchema\"": "\"{\\n \\\"type\\\": \\\"object\\\",\\n \\\"properties\\\": {\\n \\\"article\\\": {\\n \\\"type\\\": \\\"object\\\",\\n \\\"required\\\": [\\\"category\\\", \\\"title\\\", \\\"metadata\\\", \\\"content\\\", \\\"hashtags\\\"],\\n \\\"properties\\\": {\\n \\\"category\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Article category\\\"\\n },\\n \\\"title\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Article title\\\"\\n },\\n \\\"metadata\\\": {\\n \\\"type\\\": \\\"object\\\",\\n \\\"properties\\\": {\\n \\\"timePosted\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Time since article was posted\\\"\\n },\\n \\\"author\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Article author name\\\"\\n },\\n \\\"tag\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Article primary tag\\\"\\n }\\n },\\n \\\"required\\\": [\\\"timePosted\\\", \\\"author\\\", \\\"tag\\\"]\\n },\\n \\\"content\\\": {\\n \\\"type\\\": \\\"object\\\",\\n \\\"properties\\\": {\\n \\\"mainText\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Main article content\\\"\\n },\\n \\\"sections\\\": {\\n \\\"type\\\": \\\"array\\\",\\n \\\"items\\\": {\\n \\\"type\\\": \\\"object\\\",\\n \\\"properties\\\": {\\n \\\"title\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Section title\\\"\\n },\\n \\\"text\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Section content\\\"\\n },\\n \\\"quote\\\": {\\n \\\"type\\\": \\\"string\\\",\\n \\\"description\\\": \\\"Blockquote text\\\"\\n }\\n },\\n \\\"required\\\": [\\\"title\\\", \\\"text\\\", \\\"quote\\\"]\\n }\\n }\\n },\\n \\\"required\\\": [\\\"mainText\\\", \\\"sections\\\"]\\n },\\n \\\"hashtags\\\": {\\n \\\"type\\\": \\\"array\\\",\\n \\\"items\\\": {\\n \\\"type\\\": \\\"string\\\"\\n },\\n \\\"description\\\": \\\"Article hashtags\\\"\\n }\\n }\\n }\\n }\\n}\"", "\"webhookId\"": "\"6a8e3ae7-02ae-4663-a27a-07df448550ab\",", "\"path\"": "\"pblog\",", "\"responseMode\"": "\"responseNode\"", "\"respondWith\"": "\"text\",", "\"responseBody\"": "\"={{ $json.text }}\"", "\"text\"": "\"=Convert this verbatim into HTML: {{ $json.article.toJsonString() }}\\n\\n## Formatting Guidelines\\n- HTML document must be single line document without tabs or line breaks\\n- Use proper HTML tags throughout\\n- Do not use these tags: <html> <body> <style> <head>\\n- Use <h1> tag for main title\\n- Use <h2> tags for secondary titles\\n- Structure with <p> tags for paragraphs\\n- Include appropriate spacing\\n- Use <blockquote> for direct quotes\\n- Maintain consistent formatting\\n- Write in clear, professional tone\\n- Break up long paragraphs\\n- Use engaging subheadings\\n- Include transitional phrases\\n\\nThe final JSON response should contain only the title and content fields, with the content including all HTML formatting.\\n{\\n\\t\\\"title\\\": \\\"the title\\\",\\n\\t\\\"content\\\": \\\"the HTML\\\"\\n}\",", "\"chatId\"": "\"={{ $json.telegram_chat_id }}\",", "\"additionalFields\"": "{", "\"parse_mode\"": "\"HTML\",", "\"appendAttribution\"": "false", "\"telegramApi\"": "{", "\"promptType\"": "\"define\"", "\"conditions\"": "[", "\"version\"": "2,", "\"leftValue\"": "\"\",", "\"caseSensitive\"": "true,", "\"typeValidation\"": "\"strict\"", "\"combinator\"": "\"and\",", "\"operator\"": "{", "\"operation\"": "\"equals\"", "\"singleValue\"": "true", "\"rightValue\"": "\"\"", "\"assignments\"": "[", "\"value\"": "\"=Error. No topic provided.\"", "\"systemMessage\"": "\"Use the perplexity_research_tool to provide research on the users topic.\\n\\n\"", "\"hasOutputParser\"": "true", "\"fields\"": "{", "\"values\"": "[", "\"stringValue\"": "\"= {{ $json.text }}\"", "\"workflowId\"": "{", "\"__rl\"": "true,", "\"mode\"": "\"id\",", "\"description\"": "\"Call this tool to perform Perplexity research.\",", "\"jsonSchemaExample\"": "\"{\\n \\\"topic\\\": \\\"\\\"\\n}\"", "\"retryOnFail\"": "true,", "\"agent\"": "\"conversationalAgent\",", "\"includeOtherFields\"": "true", "\"url\"": "\"https://api.perplexity.ai/chat/completions\",", "\"method\"": "\"POST\",", "\"jsonBody\"": "\"={\\n \\\"model\\\": \\\"llama-3.1-sonar-small-128k-online\\\",\\n \\\"messages\\\": [\\n {\\n \\\"role\\\": \\\"system\\\",\\n \\\"content\\\": \\\"{{ $json.system }}\\\"\\n },\\n {\\n \\\"role\\\": \\\"user\\\",\\n \\\"content\\\": \\\"{{ $json.user }}\\\"\\n }\\n ],\\n \\\"max_tokens\\\": \\\"4000\\\",\\n \\\"temperature\\\": 0.2,\\n \\\"top_p\\\": 0.9,\\n \\\"return_citations\\\": true,\\n \\\"search_domain_filter\\\": [\\n \\\"perplexity.ai\\\"\\n ],\\n \\\"return_images\\\": false,\\n \\\"return_related_questions\\\": false,\\n \\\"search_recency_filter\\\": \\\"month\\\",\\n \\\"top_k\\\": 0,\\n \\\"stream\\\": false,\\n \\\"presence_penalty\\\": 0,\\n \\\"frequency_penalty\\\": 1\\n}\",", "\"sendBody\"": "true,", "\"specifyBody\"": "\"json\",", "\"authentication\"": "\"genericCredentialType\",", "\"genericAuthType\"": "\"httpHeaderAuth\"", "\"httpCustomAuth\"": "{", "\"httpHeaderAuth\"": "{", "\"active\"": "false,", "\"pinData\"": "{},", "\"settings\"": "{", "\"executionOrder\"": "\"v1\"", "\"versionId\"": "\"9ebf0569-4d9d-4783-b797-e5df2a8e8415\",", "\"connections\"": "{", "\"If\"": "{", "\"main\"": "[", "\"node\"": "\"Perplexity Topic Agent\",", "\"index\"": "0", "\"If2\"": "{", "\"Article\"": "{", "\"Chat Id\"": "{", "\"If HTML\"": "{", "\"Prompts\"": "{", "\"Webhook\"": "{", "\"Chat Id1\"": "{", "\"Contents\"": "{", "\"If Topic\"": "{", "\"Get Topic\"": "{", "\"Telegram2\"": "{", "\"If Article\"": "{", "\"Perplexity\"": "{", "\"gpt-4o-mini\"": "{", "\"ai_languageModel\"": "[", "\"Extract JSON\"": "{", "\"gpt-4o-mini1\"": "{", "\"gpt-4o-mini2\"": "{", "\"gpt-4o-mini3\"": "{", "\"gpt-4o-mini5\"": "{", "\"Basic LLM Chain\"": "{", "\"If Topic Exists\"": "{", "\"Create HTML Article\"": "{", "\"Improve Users Topic\"": "{", "\"Perplexity Topic Agent\"": "{", "\"Execute Workflow Trigger\"": "{", "\"No Operation, do nothing\"": "{", "\"Structured Output Parser1\"": "{", "\"ai_outputParser\"": "[", "\"Call Perplexity Researcher\"": "{", "\"ai_tool\"": "["}