{"name": "Facebook Simple Analysis + Telegram", "nodes": [{"id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-500, -200], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 12}]}}, "typeVersion": 1.2}, {"id": "manual-trigger", "name": "Manual Test", "type": "n8n-nodes-base.manualTrigger", "position": [-500, -50], "parameters": {}, "typeVersion": 1}, {"id": "http-request", "name": "Get Facebook Page", "type": "n8n-nodes-base.httpRequest", "position": [-200, -120], "parameters": {"url": "https://www.facebook.com/YOUR_PAGE_NAME", "options": {"headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}}, "typeVersion": 4.2}, {"id": "html-extract", "name": "Extract Posts", "type": "n8n-nodes-base.htmlExtract", "position": [100, -120], "parameters": {"extractionValues": {"values": [{"key": "posts", "cssSelector": "[data-testid='post_message']", "returnArray": true}]}}, "typeVersion": 1}, {"id": "ai-analysis", "name": "AI Analysis", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [400, -120], "parameters": {"text": "=🔍 **ANALYSE DES POSTS FACEBOOK**\n\nAnalyse ces posts et fournis un rapport structuré :\n\n**Posts à analyser :**\n{{ $json.posts ? $json.posts.join('\\n\\n---\\n\\n') : 'Aucun post trouvé' }}\n\n**Fournis :**\n📊 **1. STATISTIQUES GÉNÉRALES**\n- Nombre de posts analysés\n- Période couverte\n\n🎯 **2. THÈMES PRINCIPAUX**\n- Top 3 des sujets les plus abordés\n- Mots-clés récurrents\n\n😊 **3. ANALYSE DE SENTIMENT**\n- % de posts positifs/négatifs/neutres\n- Ton général de la communauté\n\n📈 **4. TENDANCES**\n- Évolution des sujets\n- Patterns identifiés\n\n💡 **5. RECOMMANDATIONS**\n- Suggestions pour améliorer l'engagement\n- Opportunités identifiées\n\n**Format :** Utilise des emojis et reste concis (max 3000 caractères)", "promptType": "define"}, "typeVersion": 1.5}, {"id": "gemini-model", "name": "Google Gemini", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [500, 50], "parameters": {"options": {"temperature": 0.4}, "modelName": "models/gemini-2.0-flash-lite-preview"}, "typeVersion": 1}, {"id": "telegram-send", "name": "Send to Telegram", "type": "n8n-nodes-base.telegram", "position": [700, -120], "parameters": {"text": "=📊 **RAPPORT FACEBOOK** 📅 {{ new Date().toLocaleDateString('fr-FR') }}\n\n{{ $json.text.slice(0, 3800) }}", "chatId": "YOUR_TELEGRAM_CHAT_ID", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "typeVersion": 1.2}], "connections": {"Schedule Trigger": {"main": [[{"node": "Get Facebook Page", "type": "main", "index": 0}]]}, "Manual Test": {"main": [[{"node": "Get Facebook Page", "type": "main", "index": 0}]]}, "Get Facebook Page": {"main": [[{"node": "Extract Posts", "type": "main", "index": 0}]]}, "Extract Posts": {"main": [[{"node": "AI Analysis", "type": "main", "index": 0}]]}, "AI Analysis": {"main": [[{"node": "Send to Telegram", "type": "main", "index": 0}]]}, "Google Gemini": {"ai_languageModel": [[{"node": "AI Analysis", "type": "ai_languageModel", "index": 0}]]}}}