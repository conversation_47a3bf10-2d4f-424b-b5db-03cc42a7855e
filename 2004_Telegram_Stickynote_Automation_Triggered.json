{"id": "wi2ZWKN9XPR0jkvn", "meta": {"instanceId": "a5283507e1917a33cc3ae615b2e7d5ad2c1e50955e6f831272ddd5ab816f3fb6", "templateCredsSetupCompleted": true}, "name": "OpenSea AI-Powered Insights via Telegram", "tags": [], "nodes": [{"id": "0b6ec133-7023-4c6a-ae53-78168211545c", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [840, 140], "webhookId": "befa3e52-7b57-4832-9f88-b2c430244595", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "787a9e8d-e67d-4947-90d1-8e3284de7b39", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [840, -160], "webhookId": "f9267d32-3860-4f02-99b3-493c4cac36ed", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "R3vpGq0SURbvEw2Z", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "2e10802a-48d7-4b82-afe0-b9e5f93498bf", "name": "Adds SessionId", "type": "n8n-nodes-base.set", "position": [1160, -160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b5c25cd4-226b-4778-863f-79b13b4a5202", "name": "sessionId", "type": "string", "value": "={{ $json.message.chat.id }}"}]}, "includeOtherFields": true}, "typeVersion": 3.4}, {"id": "8dd2dcdd-7cd5-4381-b1a5-66a2b6a69111", "name": "Opensea Supervisor Brain", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1380, 160], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "yUizd8t0sD5wMYVG", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "b2b59481-afbb-4cb6-98b7-c26bf51ead76", "name": "Opensea Supervisor Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1580, 160], "parameters": {}, "typeVersion": 1.3}, {"id": "52dde53b-cb42-4ae2-b573-d9356d7ec3f3", "name": "OpenSea Analytics Agent Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1760, 160], "parameters": {"name": "OpenSea_Analytics_Agent_Tool", "workflowId": {"__rl": true, "mode": "list", "value": "yRMCUm6oJEMknhbw", "cachedResultName": "JayaFamily Assistant — OpenSea Analytics Agent <PERSON>"}, "workflowInputs": {"value": {"message": "={{ $fromAI(\"message\",\"Populate this with a relevant message to this subagent\")}}", "sessionId": "={{ $json.sessionId }}"}, "schema": [{"id": "message", "type": "string", "display": true, "removed": false, "required": false, "displayName": "message", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sessionId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "596517b1-4f1e-4285-b8ee-cdf8108c4138", "name": "OpenSea NFT Agent Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1940, 160], "parameters": {"name": "OpenSea_NFT_Agent_Tool", "workflowId": {"__rl": true, "mode": "list", "value": "ZBH1ExE58wsoodkZ", "cachedResultName": "JayaFamily Assistant — OpenSea NFT Agent Tool"}, "workflowInputs": {"value": {"message": "={{ $fromAI(\"message\",\"Populate this with a relevant message to this subagent\")}}", "sessionId": "={{ $json.sessionId }}"}, "schema": [{"id": "message", "type": "string", "display": true, "removed": false, "required": false, "displayName": "message", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sessionId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "643c5c81-ba21-4afa-9c78-70cd6cde08f7", "name": "OpenSea Marketplace Agent Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [2120, 160], "parameters": {"name": "OpenSea_Marketplace_Agent_Tool", "workflowId": {"__rl": true, "mode": "list", "value": "brRSLvIkYp3mLq0K", "cachedResultName": "JayaFamily Assistant — OpenSea Marketplace Agent Tool"}, "workflowInputs": {"value": {"message": "={{ $fromAI(\"message\",\"Populate this with a relevant message to this subagent\")}}", "sessionId": "={{ $json.sessionId }}"}, "schema": [{"id": "message", "type": "string", "display": true, "removed": false, "required": false, "displayName": "message", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sessionId", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sessionId", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2}, {"id": "069cb9bc-96a4-4539-b7c5-b06d29968ec6", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [2080, -120], "webhookId": "9841771a-821a-4a40-a9e8-fb8a29eaa9f3", "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "R3vpGq0SURbvEw2Z", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "cc852b55-0214-4276-9c2f-755d9cb3fc28", "name": "OpenSea AI-Powered Insights Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1600, -120], "parameters": {"text": "={{ $json.message.text }}", "options": {"systemMessage": "**🌍 Role & Capabilities**  \nThe **OpenSea AI-Powered Insights Agent** is an advanced **AI data analyst** with **full access to OpenSea’s API**, capable of executing **multi-step queries, data aggregation, and deep research** into NFT market trends, asset tracking, and real-time trading insights.  \n\nIt leverages **three powerful agent tools** to provide **actionable insights and decision-making intelligence**:  \n1. **Marketplace Agent** – Fetches **listings, orders, offers, and trait-based pricing data**.  \n2. **Analytics Agent** – Retrieves **NFT collection statistics, transaction histories, and market analytics**.  \n3. **NFT Agent** – Gathers **detailed metadata, ownership details, and payment token data**.  \n\n🧠 **This AI system can process multiple tools together, conduct research between datasets, and synthesize powerful responses to user queries.**  \n\n---\n\n## **🛠 Actionable Insights & Multi-Step Queries**\nThe agent can **combine** multiple tools, process collected data, and execute deep research for **smarter responses**.  \n\n🔹 **How this works**:\n- 🏛 **Compare multiple collections** _(e.g., floor price, sales volume)_  \n- 🎯 **Track NFT flipping trends** _(e.g., which wallets buy/sell the most)_  \n- 🔥 **Identify undervalued NFTs** _(e.g., listings below average trait value)_  \n- 📊 **Aggregate sales data over time** _(e.g., 7-day vs. 30-day collection trends)_  \n- 👥 **Analyze whale movements** _(e.g., track large NFT purchases)_  \n- 💡 **Predict market shifts** _(e.g., sudden spikes in buy offers)_  \n\n📢 **Example Action Queries:**  \n- _“Find me the top 5 most undervalued Azuki NFTs based on recent sales.”_  \n- _“Compare the last 3 months of trading volume between Moonbirds and CloneX.”_  \n- _“Track all wallets that recently sold a Bored Ape Yacht Club NFT.”_  \n- _“List the top 10 wallets making the most NFT purchases this week.”_  \n\n---\n\n## **🚀 Available Tools & Proper Usage**  \n\n### **1️⃣ Marketplace Agent Tools (Orders, Listings, and Offers)**\nProvides **real-time marketplace data** for **NFTs, collections, and traits**.  \n\n🔹 **How to use these tools correctly**:\n- Always input **a valid OpenSea collection slug** (found in OpenSea URLs).  \n- Ensure **blockchain names** match OpenSea’s supported chains.  \n- Use **pagination (`next` cursor)** for large datasets.  \n\n| **Tool**                        | **Description** |\n|----------------------------------|----------------|\n| 🛒 **Get All Listings (by Collection)**  | Fetches all active listings for a collection. |\n| 💰 **Get All Offers (by Collection)**  | Retrieves all valid offers for a collection. |\n| 🔎 **Get Best Listing (by NFT)**  | Finds the **cheapest** active listing for a specific NFT. |\n| 🏆 **Get Best Listings (by Collection)** | Retrieves the **cheapest** active listings for an entire collection. |\n| 💲 **Get Best Offer (by NFT)** | Finds the **highest** offer for a specific NFT. |\n| 🏷 **Get Collection Offers** | Retrieves all active **collection-wide** offers. |\n| 🎯 **Get Item Offers** | Fetches **individual** offers, excluding criteria-based offers. |\n| 📋 **Get Listings (by Chain & Protocol)** | Lists all active orders filtered by blockchain and protocol. |\n| 🔗 **Get Order (by Hash)** | Retrieves details for a **specific order** using its hash. |\n| 🎨 **Get Trait Offers** | Retrieves **all trait-based offers** in a collection. |\n\n✅ **Critical Notes for Marketplace Queries**:\n1. **Only use OpenSea’s supported chains** _(see the full list below)_.  \n2. `\"polygon\"` is **not allowed** – use `\"matic\"` instead.  \n3. **Seaport is the only supported protocol** for order-related queries.  \n4. **Fixed protocol address** for Get Order:  \n   - `******************************************`  \n5. **Pagination**: Use `next` parameter for large datasets.  \n\n---\n\n### **2️⃣ Analytics Agent Tools (Market Insights & Transactions)**\nDelivers **historical and real-time analysis** on NFT collections, user transactions, and blockchain events.  \n\n🔹 **How to use these tools correctly**:\n- Always specify **a valid collection slug** or **wallet address**.  \n- Filter transactions by **blockchain, event type, and timeframe**.  \n- Use **pagination** when fetching large datasets.  \n\n| **Tool**                          | **Description** |\n|------------------------------------|----------------|\n| 📊 **Get Collection Stats** | Fetches **market cap, floor price, total volume, and sales** of an NFT collection. |\n| 🏷 **Get Events (All Market Activity)** | Retrieves **all NFT events** including sales, transfers, listings, bids, and redemptions. |\n| 👤 **Get Events (by Account)** | Lists **all NFT-related transactions** for a specific **wallet address**. |\n| 🏛 **Get Events (by Collection)** | Fetches all transactions for **an entire NFT collection**. |\n| 🎟 **Get Events (by NFT)** | Retrieves the **full transaction history** of a single NFT. |\n\n✅ **Critical Notes for Analytics Queries**:\n1. Use **valid blockchain names** _(see list below)_ to filter results.  \n2. Set **event types**: _sale, transfer, listing, bid, redemption_.  \n3. Use `before` and `after` timestamps _(Unix format)_ to filter historical data.  \n4. **Pagination**: Use `next` for large datasets.  \n\n---\n\n### **3️⃣ NFT Agent Tools (Metadata, Ownership, and Smart Contracts)**\nProvides **in-depth details** about individual NFTs, collections, and payment tokens.  \n\n🔹 **How to use these tools correctly**:\n- Ensure **wallet addresses and contract addresses** are **valid**.  \n- For **NFT metadata**, provide **blockchain name + contract address + token ID**.  \n\n| **Tool**                        | **Description** |\n|----------------------------------|----------------|\n| 🔍 **Get Account** | Fetches **profile details** of an OpenSea user. |\n| 🏛 **Get Collection** | Retrieves **metadata, fees, and social links** of an NFT collection. |\n| 📜 **Get Collections** | Lists **all NFT collections** with optional filters (creator, blockchain, etc.). |\n| 🏗 **Get Contract** | Retrieves **smart contract details** for an NFT collection. |\n| 🎭 **Get NFT** | Fetches **metadata, traits, rarity, and ownership** of a single NFT. |\n| 👥 **Get NFTs (by Account)** | Lists **all NFTs owned** by a given wallet address. |\n| 📦 **Get NFTs (by Collection)** | Retrieves **multiple NFTs** from a specific collection. |\n| 🔗 **Get NFTs (by Contract)** | Lists all NFTs for a **given smart contract**. |\n| 💵 **Get Payment Token** | Retrieves **details about an ERC-20 payment token**. |\n| 🎨 **Get Traits** | Lists **all available traits** in a collection. |\n\n✅ **Critical Notes for NFT Queries**:\n1. **Use correct blockchain names** _(see full list below)_.  \n2. **Contract addresses** must be **valid** and exist on OpenSea.  \n3. **NFT Token ID is required** for fetching metadata.  \n4. **For payment tokens, ensure the correct blockchain name is used.**  \n\n---\n\n## **🚀 Supported Blockchains**\nTo avoid errors, **only use the following blockchain names**:\n\n✅ **Valid Chains for OpenSea Queries**:\n- `amoy`\n- `ape_chain`\n- `ape_curtis`\n- `arbitrum`\n- `arbitrum_nova`\n- `arbitrum_sepolia`\n- `avalanche`\n- `avalanche_fuji`\n- `b3`\n- `b3_sepolia`\n- `baobab`\n- `base`\n- `base_sepolia`\n- `bera_chain`\n- `blast`\n- `blast_sepolia`\n- `ethereum`\n- `flow`\n- `flow_testnet`\n- `klaytn`\n- `matic` _(use instead of \"polygon\")_\n- `monad_testnet`\n- `mumbai`\n- `optimism`\n- `optimism_sepolia`\n- `sei_testnet`\n- `sepolia`\n- `shape`\n- `solana`\n- `soldev`\n- `soneium`\n- `soneium_minato`\n- `unichain`\n- `zora`\n- `zora_sepolia`\n\n❌ **Do NOT use unsupported chain names!**  \n\n---\n\n## **🛠 How the AI Agent Works**\n1. **Understands your query** and determines the correct API tool.  \n2. **Executes the API request** with valid parameters.  \n3. **Processes and structures results** into **readable insights**.  \n4. **Combines multiple data sources** for research-driven responses.  \n5. **Allows follow-up questions** for deeper market insights.  \n\n🎯 **Use this AI for market intelligence, trend analysis, and NFT investment strategies!** 🚀"}, "promptType": "define"}, "typeVersion": 1.8}, {"id": "087fad83-0a96-42f6-92b1-06685bfc13f4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-940, -1380], "parameters": {"color": 2, "width": 1320, "height": 1780, "content": "# OpenSea AI-Powered Insights System (n8n) - Full Integration Guide\n\n## 🚀 System Overview\nThe **OpenSea AI-Powered Insights System** is a fully automated n8n workflow that connects multiple agent tools to deliver **real-time NFT market insights via Telegram**. This system consists of **four interconnected workflows**:\n\n1. **OpenSea AI-Powered Insights via Telegram** (Main Supervisor)  \n2. **OpenSea Analytics Agent Tool** (Market Trends & Collection Stats)  \n3. **OpenSea Marketplace Agent Tool** (Live Listings, Offers, and Orders)  \n4. **OpenSea NFT Agent Tool** (Metadata, Ownership & Payment Tokens)\n\nThese agents work **in sync** under the **Supervisor AI**, which determines the appropriate agent(s) to use based on user queries. Responses are structured and sent back via **Telegram** for real-time insights.\n\n---\n\n## 🔗 **System Architecture**\n\n### **🔹 Core Workflow: OpenSea AI-Powered Insights via Telegram**\n- Acts as the **brain and command center**.\n- Receives queries from **Telegram Chat**.\n- Determines which **agent(s)** should process the request.\n- Aggregates and formats results.\n- Sends structured responses back to the Telegram user.\n\n### **🔹 Supporting Agent Tools**\nEach **agent tool** is a separate n8n workflow with a specific function:\n\n1️⃣ **OpenSea Analytics Agent** → Retrieves **market trends, sales history, transaction data**.  \n2️⃣ **OpenSea Marketplace Agent** → Fetches **NFT listings, offers, best prices, and order details**.  \n3️⃣ **OpenSea NFT Agent** → Retrieves **NFT metadata, ownership records, traits, and payment token data**.\n\nThe **Supervisor AI (Telegram Workflow)** calls these agent workflows as needed.\n\n---\n\n## 🛠 **Setup Instructions**\n\n### **1️⃣ Setting Up the Main Supervisor (Telegram Workflow)**\n1. **Create a Telegram Bot** using [BotFather](https://t.me/botfather).\n2. **Copy the API Key** and connect it to n8n’s **Telegram Trigger Node**.\n3. Set up the **Chat Message Received Node** to capture user queries.\n4. Configure the **Session ID Node** to track conversation history.\n5. Link the **AI Supervisor Brain (GPT-4o Mini)** to process messages.\n6. Connect it to the **three agent tools** using **Tool Workflow Nodes**.\n7. Send output back to Telegram using the **Telegram Node**.\n\n✅ **This setup enables Telegram interaction with all OpenSea agents.**\n\n### **2️⃣ Configuring the OpenSea Agent Tools**\nEach agent tool must be linked to the main workflow:\n\n**A. OpenSea Analytics Agent**\n- Retrieves NFT market trends & transaction history.\n- Requires **collection slug, wallet address, or transaction filters**.\n\n**B. OpenSea Marketplace Agent**\n- Fetches NFT listings, offers, and orders.\n- Requires **collection slug, token ID, or order hash**.\n\n**C. OpenSea NFT Agent**\n- Retrieves NFT metadata, traits, and ownership data.\n- Requires **wallet address, contract address, or token ID**.\n\n### **3️⃣ Connecting the Agents to the Main Workflow**\nEach **Tool Workflow Node** inside the **Telegram Supervisor Workflow** must be configured to pass the query **to the correct agent tool**.\n\nExample:\n- User asks: **“Find the cheapest listing for Bored Ape #1234”** → **Marketplace Agent is activated**.\n- User asks: **“Retrieve all NFTs owned by 0xABC...”** → **NFT Agent is activated**.\n- User asks: **“Compare last 3 months’ sales volume of Azuki and Moonbirds”** → **Analytics Agent is activated**.\n\n---\n\n\n"}, "typeVersion": 1}, {"id": "c9dd93eb-44bc-4825-a092-8a8b8e3b07bb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [660, -1380], "parameters": {"color": 5, "width": 840, "height": 1060, "content": "## 🔄 **Data Flow & Execution Process**\n\n### **📩 Step 1: Receiving a Query**\n1. User sends a request via Telegram.\n2. The **Telegram Trigger** captures the message.\n3. The **Session ID Node** assigns a conversation ID.\n\n### **📊 Step 2: Processing the Query**\n4. The **AI Supervisor Brain** (GPT-4o Mini) interprets the request.\n5. It decides which **agent tool** should process the query.\n\n### **🔗 Step 3: Activating the Correct Agent Tool**\n6. The appropriate **Tool Workflow Node** is triggered (Analytics, Marketplace, or NFT Agent).\n7. The **selected agent processes the query** and fetches data from OpenSea’s API.\n\n### **📤 Step 4: Sending the Response**\n8. The response is structured by the AI Supervisor.\n9. The **Telegram Node** sends the formatted answer to the user.\n\n✅ **This ensures that all agents work together seamlessly.**\n\n---\n\n## 🔥 **Example Queries & Expected Outputs**\n\n### **🛒 OpenSea Marketplace Queries**\n| **User Query** | **Agent Used** | **Expected Response** |\n|--------------|--------------|----------------|\n| _“Show me the 5 cheapest listings for Azuki.”_ | Marketplace Agent | List of Azuki NFTs with prices & links. |\n| _“What’s the highest offer on Bored Ape #4567?”_ | Marketplace Agent | Highest active bid with buyer info. |\n| _“Fetch the details of order 0x123abc...”_ | Marketplace Agent | Order breakdown (price, seller, expiration). |\n\n### **📊 OpenSea Analytics Queries**\n| **User Query** | **Agent Used** | **Expected Response** |\n|--------------|--------------|----------------|\n| _“Compare 7-day sales volume of BAYC & MAYC.”_ | Analytics Agent | Chart showing sales data. |\n| _“List all transactions for CloneX in the last 24 hours.”_ | Analytics Agent | Table of sales & transfers. |\n| _“Track all wallets that sold a Doodle in the last week.”_ | Analytics Agent | List of wallets & sold NFTs. |\n\n### **🎭 OpenSea NFT Metadata Queries**\n| **User Query** | **Agent Used** | **Expected Response** |\n|--------------|--------------|----------------|\n| _“Retrieve metadata for Cool Cat #7890.”_ | NFT Agent | NFT description, image, and attributes. |\n| _“Which NFTs does 0x123... own on Ethereum?”_ | NFT Agent | List of NFTs held by the wallet. |\n| _“Show me all NFTs from contract 0xABC...”_ | NFT Agent | All tokens linked to the contract. |\n\n✅ **This demonstrates how each agent provides unique insights.**\n\n---\n\n"}, "typeVersion": 1}, {"id": "5f8504a0-283a-408e-9768-b4b90088e687", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1800, -1380], "parameters": {"color": 3, "width": 800, "height": 720, "content": "## ⚠️ **Critical Setup Notes & Troubleshooting**\n\n🔹 **1. Ensure Correct API Credentials**\n- Each agent must be connected to OpenSea’s API.\n- Use **HTTP Header Authentication** with an **API Key**.\n\n🔹 **2. Check for Invalid Chain Names**\n- ❌ `\"polygon\"` is **not valid** → Use `\"matic\"` instead.\n- ✅ **Only supported blockchains should be used**.\n\n🔹 **3. Maintain Session Tracking**\n- Ensure **sessionId** is passed correctly between workflows.\n- This prevents **context loss** in multi-step queries.\n\n🔹 **4. Use Pagination for Large Datasets**\n- For queries returning **100+ results**, use the `next` parameter.\n\n---\n\n## 🚀 **Final Thoughts**\nThe **OpenSea AI-Powered Insights System** is designed for **NFT investors, collectors, and analysts** seeking **real-time, structured market data** through Telegram. By integrating multiple agent tools, it provides a **powerful, automated way to analyze NFTs, transactions, and market trends**.\n\n**Need Help?**  \n🌐 Connect on LinkedIn:  \n🔗 [http://linkedin.com/in/donjayamahajr](http://linkedin.com/in/donjayamahajr)\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "184b0a31-6aee-4b9b-adc5-ef06e6a3f3f0", "connections": {"Adds SessionId": {"main": [[{"node": "OpenSea AI-Powered Insights Agent", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Adds SessionId", "type": "main", "index": 0}]]}, "OpenSea NFT Agent Tool": {"ai_tool": [[{"node": "OpenSea AI-Powered Insights Agent", "type": "ai_tool", "index": 0}]]}, "Opensea Supervisor Brain": {"ai_languageModel": [[{"node": "OpenSea AI-Powered Insights Agent", "type": "ai_languageModel", "index": 0}]]}, "Opensea Supervisor Memory": {"ai_memory": [[{"node": "OpenSea AI-Powered Insights Agent", "type": "ai_memory", "index": 0}]]}, "When chat message received": {"main": [[{"node": "OpenSea AI-Powered Insights Agent", "type": "main", "index": 0}]]}, "OpenSea Analytics Agent Tool": {"ai_tool": [[{"node": "OpenSea AI-Powered Insights Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea Marketplace Agent Tool": {"ai_tool": [[{"node": "OpenSea AI-Powered Insights Agent", "type": "ai_tool", "index": 0}]]}, "OpenSea AI-Powered Insights Agent": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}}}