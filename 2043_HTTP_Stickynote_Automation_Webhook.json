{"id": "IyhH1KHtXidKNSIA", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef"}, "name": "🐋DeepSeek V3 Chat & R1 Reasoning Quick Start", "tags": [], "nodes": [{"id": "54c59cae-fbd0-4f0d-b633-6304e6c66d89", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-840, -740], "webhookId": "b740bd14-1b9e-4b1b-abd2-1ecf1184d53a", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "ef85680e-569f-4e74-a1b4-aae9923a0dcb", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "onError": "continueErrorOutput", "position": [-320, 40], "parameters": {"agent": "conversationalAgent", "options": {"systemMessage": "You are a helpful assistant."}}, "retryOnFail": true, "typeVersion": 1.7, "alwaysOutputData": true}, {"id": "07a8c74c-768e-4b38-854f-251f2fe5b7bf", "name": "DeepSeek", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-360, 220], "parameters": {"model": "=deepseek-reasoner", "options": {}}, "credentials": {"openAiApi": {"id": "MSl7SdcvZe0SqCYI", "name": "deepseek"}}, "typeVersion": 1.1}, {"id": "a6d58a8c-2d16-4c91-adde-acac98868150", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-220, 220], "parameters": {}, "typeVersion": 1.3}, {"id": "401a5932-9f3e-4b17-a531-3a19a6a7788a", "name": "Basic LLM Chain2", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-320, -800], "parameters": {"messages": {"messageValues": [{"message": "You are a helpful assistant."}]}}, "typeVersion": 1.5}, {"id": "215dda87-faf7-4206-bbc3-b6a6b1eb98de", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-440, -460], "parameters": {"color": 5, "width": 420, "height": 340, "content": "## DeepSeek using HTTP Request\n### DeepSeek Reasoner R1\nhttps://api-docs.deepseek.com/\nRaw Body"}, "typeVersion": 1}, {"id": "6457c0f7-ad02-4ad3-a4a0-9a7a6e8f0f7f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-440, -900], "parameters": {"color": 4, "width": 580, "height": 400, "content": "## DeepSeek with Ollama Local Model"}, "typeVersion": 1}, {"id": "2ac8b41f-b27d-4074-abcc-430a8f5928e8", "name": "Ollama DeepSeek", "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "position": [-320, -640], "parameters": {"model": "deepseek-r1:14b", "options": {"format": "default", "numCtx": 16384, "temperature": 0.6}}, "credentials": {"ollamaApi": {"id": "7aPaLgwpfdMWFYm9", "name": "Ollama account 127.0.0.1"}}, "typeVersion": 1}, {"id": "37a94fc0-eff3-4226-8633-fb170e5dcff2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-440, -80], "parameters": {"color": 3, "width": 600, "height": 460, "content": "## DeepSeek Conversational Agent w/Memory\n"}, "typeVersion": 1}, {"id": "52b484bb-1693-4188-ba55-643c40f10dfc", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [20, -460], "parameters": {"color": 6, "width": 420, "height": 340, "content": "## DeepSeek using HTTP Request\n### DeepSeek Chat V3\nhttps://api-docs.deepseek.com/\nJSON Body"}, "typeVersion": 1}, {"id": "ec46acef-60f6-4d34-b636-3654125f5897", "name": "DeepSeek JSON Body", "type": "n8n-nodes-base.httpRequest", "position": [160, -320], "parameters": {"url": "https://api.deepseek.com/chat/completions", "method": "POST", "options": {}, "jsonBody": "={\n \"model\": \"deepseek-chat\",\n \"messages\": [\n {\n \"role\": \"system\",\n \"content\": \"{{ $json.chatInput }}\"\n },\n {\n \"role\": \"user\",\n \"content\": \"Hello!\"\n }\n ],\n \"stream\": false\n}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "9CsntxjSlce6yWbN", "name": "deepseek"}}, "typeVersion": 4.2}, {"id": "e5295120-57f9-4e02-8b73-f00e4d6baa48", "name": "DeepSeek Raw Body", "type": "n8n-nodes-base.httpRequest", "position": [-300, -320], "parameters": {"url": "https://api.deepseek.com/chat/completions", "body": "={\n \"model\": \"deepseek-reasoner\",\n \"messages\": [\n {\"role\": \"user\", \"content\": \"{{ $json.chatInput.trim() }}\"}\n ],\n \"stream\": false\n }", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "authentication": "genericCredentialType", "rawContentType": "application/json", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "9CsntxjSlce6yWbN", "name": "deepseek"}}, "typeVersion": 4.2}, {"id": "571dc713-ce54-4330-8bdd-94e057ecd223", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -460], "parameters": {"color": 7, "width": 580, "height": 840, "content": "# Your First DeepSeek API Call\n\nThe DeepSeek API uses an API format compatible with OpenAI. By modifying the configuration, you can use the OpenAI SDK or softwares compatible with the OpenAI API to access the DeepSeek API.\n\nhttps://api-docs.deepseek.com/\n\n## Configuration Parameters\n\n| Parameter | Value |\n|-----------|--------|\n| base_url | https://api.deepseek.com |\n| api_key | https://platform.deepseek.com/api_keys |\n\n\n\n## Important Notes\n\n- To be compatible with OpenAI, you can also use `https://api.deepseek.com/v1` as the base_url. Note that the v1 here has NO relationship with the model's version.\n\n- The deepseek-chat model has been upgraded to DeepSeek-V3. The API remains unchanged. You can invoke DeepSeek-V3 by specifying `model='deepseek-chat'`.\n\n- deepseek-reasoner is the latest reasoning model, DeepSeek-R1, released by DeepSeek. You can invoke DeepSeek-R1 by specifying `model='deepseek-reasoner'`."}, "typeVersion": 1}, {"id": "f0ac3f32-218e-4488-b67f-7b7f7e8be130", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1060, -900], "parameters": {"color": 2, "width": 580, "height": 400, "content": "## Four Examples for Connecting to DeepSeek\nhttps://api-docs.deepseek.com/\nhttps://platform.deepseek.com/api_keys"}, "typeVersion": 1}, {"id": "91642d68-ab5d-4f61-abaf-8cb7cb991c29", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-180, -640], "parameters": {"color": 7, "width": 300, "height": 120, "content": "### Ollama Local\nhttps://ollama.com/\nhttps://ollama.com/library/deepseek-r1"}, "typeVersion": 1}], "active": false, "pinData": {"When chat message received": [{"json": {"action": "sendMessage", "chatInput": "provide 10 sentences that end in the word apple.", "sessionId": "68cb82d504c14f5eb80bdf2478bd39bb"}}]}, "settings": {"executionOrder": "v1"}, "versionId": "e354040e-7898-4ff9-91a2-b6d36030dac8", "connections": {"AI Agent": {"main": [[]]}, "DeepSeek": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Ollama DeepSeek": {"ai_languageModel": [[{"node": "Basic LLM Chain2", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Basic LLM Chain2", "type": "main", "index": 0}]]}}}