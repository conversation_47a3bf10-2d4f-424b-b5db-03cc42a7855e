{"id": "6zSE618gr9fDtAfF", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "🤖🧑‍💻 AI Agent for Top n8n Creators Leaderboard Reporting", "tags": [], "nodes": [{"id": "5b9537db-41d3-4d8a-bf41-f875e4044224", "name": "stats_aggregate_creators", "type": "n8n-nodes-base.httpRequest", "position": [-1240, 1300], "parameters": {"url": "={{ $json.path }}{{ $json['creators-filename'] }}.json", "options": {}}, "typeVersion": 4.2}, {"id": "feb2328b-57b0-4280-98d8-6b946db0c947", "name": "stats_aggregate_workflows", "type": "n8n-nodes-base.httpRequest", "position": [-1240, 1500], "parameters": {"url": "={{ $json.path }}{{ $json['workflows-filename'] }}.json", "options": {}}, "typeVersion": 4.2}, {"id": "53f8b825-b030-4541-b12b-6df6702f7d1b", "name": "Global Variables", "type": "n8n-nodes-base.set", "position": [-1660, 1460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4bcb91c6-d250-4cb4-8ee1-022df13550e1", "name": "path", "type": "string", "value": "https://raw.githubusercontent.com/teds-tech-talks/n8n-community-leaderboard/refs/heads/main/"}, {"id": "a910a798-0bfe-41b1-a4f1-41390c7f6997", "name": "workflows-filename", "type": "string", "value": "=stats_aggregate_workflows"}, {"id": "e977e816-dc1e-43ce-9393-d6488e6832ca", "name": "creators-filename", "type": "string", "value": "=stats_aggregate_creators"}, {"id": "14233ab4-3fa4-4e26-8032-6ffe26cb601e", "name": "datetime", "type": "string", "value": "={{ $now.format('yyyy-MM-dd') }}"}]}}, "typeVersion": 3.4}, {"id": "202026ea-054f-45ae-84f6-59ec58794f1c", "name": "Parse Workflow Data", "type": "n8n-nodes-base.set", "position": [-880, 1540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "76f4b20e-519e-4d46-aeac-c6c3f98a69fd", "name": "data", "type": "array", "value": "={{ $json.data }}"}]}}, "typeVersion": 3.4}, {"id": "54ecfc96-0f5e-4275-a53b-f87850926d7f", "name": "Parse Creators Data", "type": "n8n-nodes-base.set", "position": [-880, 1200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "76f4b20e-519e-4d46-aeac-c6c3f98a69fd", "name": "data", "type": "array", "value": "={{ $json.data }}"}]}}, "typeVersion": 3.4}, {"id": "e590677e-a8ff-4b76-8527-e5bdc0076610", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [-680, 1820], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "7d7ef0f2-dbca-4b24-b2e5-c1236c4beb81", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-1880, 780], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {"temperature": 0.1}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "59e7066f-da3b-4461-9a52-0f8754b696ae", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-1980, 1460], "parameters": {"inputSource": "jsonExample", "jsonExample": "{\n \"query\": \n {\n \"username\": \n \"joe\"\n }\n}"}, "typeVersion": 1.1}, {"id": "********-3520-4e37-af19-977ec3bfb260", "name": "Workflow Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-1540, 780], "parameters": {"name": "n8n_creator_stats", "workflowId": "={{ $workflow.id }}", "description": "Call this tool to get n8n Creator Stats.", "jsonSchemaExample": "{\n \"username\": \"n8n creator username\"\n}", "specifyInputSchema": true}, "typeVersion": 1}, {"id": "4b2195bd-d506-4cd5-bb9d-37cf84c8cebf", "name": "creator-summary", "type": "n8n-nodes-base.convertToFile", "position": [-1140, 60], "parameters": {"options": {"fileName": "=creators-report"}, "operation": "toText", "sourceProperty": "output"}, "typeVersion": 1.1}, {"id": "ca25473a-0e19-45e0-8de5-00601c95fdf9", "name": "Workflow Response", "type": "n8n-nodes-base.set", "position": [-480, 1820], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "eeff1310-2e1c-4ea4-9107-a14b1979f74f", "name": "response", "type": "string", "value": "={{ $json.data }}"}]}}, "typeVersion": 3.4}, {"id": "c45c9bc8-e0d9-496a-bf8d-71c806c330de", "name": "Save creator-summary.md", "type": "n8n-nodes-base.readWriteFile", "position": [-940, 60], "parameters": {"options": {"append": true}, "fileName": "=C:\\\\Users\\\\<USER>\\Downloads\\\\{{ $binary.data.fileName }}-{{ $now.format('yyyy-MM-dd-hh-mm-ss') }}.md", "operation": "write"}, "typeVersion": 1}, {"id": "0cddb18b-7924-41f6-b429-a00e4c904b47", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-2060, 240], "parameters": {"color": 5, "width": 780, "height": 740, "content": "## AI Agent for n8n Creator Leaderboard Stats\nhttps://github.com/teds-tech-talks/n8n-community-leaderboard"}, "typeVersion": 1}, {"id": "6e1a7ffe-bac6-43d8-b7e8-866eb5fcb9f7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1640, 620], "parameters": {"width": 280, "height": 300, "content": "## Tool Call for n8n Creators Stats\nhttps://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.toolworkflow/"}, "typeVersion": 1}, {"id": "892ac156-a276-4697-9b25-768301991996", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1980, 620], "parameters": {"color": 7, "width": 300, "height": 300, "content": "## OpenAI LLM\nhttps://platform.openai.com/api-keys"}, "typeVersion": 1}, {"id": "1e3cdf04-b33f-4a64-83c8-f24c424380b2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1240, -60], "parameters": {"width": 540, "height": 320, "content": "## Save n8n Creators & Workflows Report Locally\n(optional for local install)"}, "typeVersion": 1}, {"id": "a01adc65-9425-460b-85ed-fac4c82f1e78", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1760, 1340], "parameters": {"width": 300, "height": 320, "content": "## Global Workflow Variables\n\n"}, "typeVersion": 1}, {"id": "f7523185-7d36-4839-bfd3-d101fc1164fa", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1800, 1100], "parameters": {"color": 3, "width": 780, "height": 640, "content": "## Daily n8n Leaderboard Stats\nhttps://github.com/teds-tech-talks/n8n-community-leaderboard\n\n### n8n Leaderboard\nhttps://teds-tech-talks.github.io/n8n-community-leaderboard/"}, "typeVersion": 1}, {"id": "79381486-6caf-4629-94ac-d7cfef44c437", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-980, 1100], "parameters": {"color": 6, "width": 1120, "height": 300, "content": "## n8n Creators Stats"}, "typeVersion": 1}, {"id": "6099f718-37d2-45a6-806c-2196dbf6736b", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-980, 1440], "parameters": {"color": 4, "width": 1120, "height": 300, "content": "## n8n Workflow Stats"}, "typeVersion": 1}, {"id": "1270338c-1a9f-4a90-a5f1-7efd7547de4e", "name": "Creators Data", "type": "n8n-nodes-base.set", "position": [-60, 1200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "02b02023-c5a2-4e22-bcf9-2284c434f5d3", "name": "name", "type": "string", "value": "={{ $json.user.name }}"}, {"id": "4582435b-3c76-45e7-a251-12055efa890a", "name": "username", "type": "string", "value": "={{ $json.user.username }}"}, {"id": "b713a971-ce29-43cf-8f42-c426a38c6582", "name": "bio", "type": "string", "value": "={{ $json.user.bio }}"}, {"id": "19a06510-802e-4bd5-9552-7afa7355ff92", "name": "sum_unique_weekly_inserters", "type": "number", "value": "={{ $json.sum_unique_weekly_inserters }}"}, {"id": "e436533a-5170-47c2-809b-7d79502eb009", "name": "sum_unique_monthly_inserters", "type": "number", "value": "={{ $json.sum_unique_monthly_inserters }}"}, {"id": "198fef5d-86b8-4009-b187-6d3e6566d137", "name": "sum_unique_inserters", "type": "number", "value": "={{ $json.sum_unique_inserters }}"}]}}, "typeVersion": 3.4}, {"id": "3fd50542-2067-4dd4-a3ae-006aa4f9b030", "name": "Workflows Data", "type": "n8n-nodes-base.set", "position": [-60, 1540], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3bc3cd11-904d-4315-974d-262c0bd5fea7", "name": "template_url", "type": "string", "value": "={{ $json.template_url }}"}, {"id": "c846c523-f077-40cd-b548-32460124ffb9", "name": "wf_detais.name", "type": "string", "value": "={{ $json.wf_detais.name }}"}, {"id": "f330de47-56fb-4657-8a30-5f5e5cfa76d7", "name": "wf_detais.createdAt", "type": "string", "value": "={{ $json.wf_detais.createdAt }}"}, {"id": "f7ed7e51-a7cf-4f2e-8819-f33115c5ad51", "name": "wf_detais.description", "type": "string", "value": "={{ $json.wf_detais.description }}"}, {"id": "02b02023-c5a2-4e22-bcf9-2284c434f5d3", "name": "name", "type": "string", "value": "={{ $json.user.name }}"}, {"id": "4582435b-3c76-45e7-a251-12055efa890a", "name": "username", "type": "string", "value": "={{ $json.user.username }}"}, {"id": "f952cad3-7e62-46b7-aeb7-a5cbf4d46c0d", "name": "unique_weekly_inserters", "type": "number", "value": "={{ $json.unique_weekly_inserters }}"}, {"id": "6123302b-5bda-48f4-9ef2-71ff52a5f3ba", "name": "unique_monthly_inserters", "type": "number", "value": "={{ $json.unique_monthly_inserters }}"}, {"id": "92dca169-e03f-42ad-8790-ebb55c1a7272", "name": "unique_weekly_visitors", "type": "number", "value": "={{ $json.unique_weekly_visitors }}"}, {"id": "ee640389-d396-4d65-8110-836372a51fb0", "name": "unique_monthly_visitors", "type": "number", "value": "={{ $json.unique_monthly_visitors }}"}, {"id": "9f1c5599-3672-4f4e-9742-d7cc564f6714", "name": "user.avatar", "type": "string", "value": "={{ $json.user.avatar }}"}]}}, "typeVersion": 3.4}, {"id": "6ad04027-1df9-402d-b98c-de7ec7e62cae", "name": "Merge Creators & Workflows", "type": "n8n-nodes-base.merge", "position": [240, 1540], "parameters": {"mode": "combine", "options": {}, "joinMode": "enrichInput1", "fieldsToMatchString": "username"}, "typeVersion": 3}, {"id": "fdf56c84-804a-46e2-8058-8a4374ba21b7", "name": "Split Out Creators", "type": "n8n-nodes-base.splitOut", "position": [-680, 1200], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "cac2e121-f0a9-4142-86c7-5549b8b3631d", "name": "Split Out Workflows", "type": "n8n-nodes-base.splitOut", "position": [-680, 1540], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "4a32eb8c-07d2-4a71-bb60-9e2c2eeda7f6", "name": "Sort By Top Weekly Creator Inserts", "type": "n8n-nodes-base.sort", "position": [-480, 1200], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"order": "descending", "fieldName": "sum_unique_weekly_inserters"}]}}, "typeVersion": 1}, {"id": "f39b2e87-cc3a-4e90-84dc-18ae663608d6", "name": "Sort By Top Weekly Workflow Inserts", "type": "n8n-nodes-base.sort", "position": [-480, 1540], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"order": "descending", "fieldName": "unique_weekly_inserters"}]}}, "typeVersion": 1}, {"id": "85ae9c6b-50bd-40df-bebd-e7522df61f3c", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-2060, 1020], "parameters": {"color": 7, "width": 2510, "height": 1000, "content": "## Workflow for n8n Creators Stats"}, "typeVersion": 1}, {"id": "7aaf6f1b-a42b-49e6-a9bd-27c8ee2b6e83", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-1340, 1140], "parameters": {"color": 7, "width": 280, "height": 560, "content": "## GET n8n Stats from GitHub repo\nhttps://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}, "typeVersion": 1}, {"id": "5aa6990b-c764-4d5a-ab68-c6f12b3d3b70", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-2260, 380], "parameters": {"rule": {"interval": [{"triggerAtHour": 22}]}}, "typeVersion": 1.2}, {"id": "160fa10e-9697-4c84-ba13-d701baaee782", "name": "Take Top 10 Creators", "type": "n8n-nodes-base.limit", "position": [-260, 1200], "parameters": {"maxItems": 10}, "typeVersion": 1}, {"id": "09d8cc25-7ea7-4793-a891-90f8b577df81", "name": "Take Top 50 Workflows", "type": "n8n-nodes-base.limit", "position": [-260, 1540], "parameters": {"maxItems": 50}, "typeVersion": 1}, {"id": "c3ebbc08-151e-4f18-848f-ddec2a720edc", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [-1040, 460], "parameters": {"name": "=n8n Creator Stats Report - {{ $now.format('yyyy-MM-dd:hh:mm:ss') }}", "content": "={{ $json.output }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "options": {}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}, "operation": "createFromText"}, "credentials": {"googleDriveOAuth2Api": {"id": "UhdXGYLTAJbsa0xX", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "0a2ff2ea-6120-49e2-adda-547830b4f9f8", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-320, 1060], "parameters": {"width": 220, "height": 720, "content": "## Settings\nChange these settings to suit your needs"}, "typeVersion": 1}, {"id": "f5db76e5-8058-4771-8a3b-0116f0abb6a3", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-1240, 300], "parameters": {"color": 6, "width": 540, "height": 340, "content": "## Save n8n Creator & Workflows Report to Google Drive\nhttps://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googledrive/"}, "typeVersion": 1}, {"id": "4594d952-8d21-40ac-8654-4a050c96a686", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-1240, 680], "parameters": {"color": 4, "width": 540, "height": 300, "content": "## Email n8n Creators & Workflows Report\nhttps://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gmail/"}, "typeVersion": 1}, {"id": "784b5047-9fdf-40db-ab07-436c12d749d0", "name": "Convert Markdown to HTML", "type": "n8n-nodes-base.markdown", "position": [-1140, 780], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json.output }}"}, "typeVersion": 1}, {"id": "cab1978f-9aa0-4cd8-901c-f6ad615936c6", "name": "n8n Creators Stats Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-1800, 380], "parameters": {"text": "=Prepare a report about the n8n creators", "options": {"systemMessage": "=You are tasked with generating a **comprehensive Markdown report** about n8n community workflows and contributors using the provided tools. Your report should include meaningful insights about the contributors positive impact on the n8n community. Follow the structure below:\n\n## Detailed Summary\n- Provide a thorough summary of ALL contributor's workflows.\n- Highlight unique features, key use cases, and notable technical components for each workflow.\n- Include hyperlinks for each workflow.\n\n## Workflows\nCreate a well-formatted markdown table with these columns:\n- **Workflow Name**: The name of the workflow. Keep the emojies of they exist. Include hyperlinks for each workflow.\n- **Description**: A brief overview of its purpose and functionality.\n- **Unique Weekly Visitors**: The number of unique users who visited this workflow weekly.\n- **Unique Monthly Visitors**: The number of unique users who visited this workflow monthly.\n- **Unique Weekly Inserters**: The number of unique users who inserted this workflow weekly.\n- **Unique Monthly Inserters**: The number of unique users who inserted this workflow monthly.\n- **Why It’s Popular**: Explain what makes this workflow stand out (e.g., innovative features, ease of use, specific use cases).\n\n## Community Analysis\n- Analyze why these workflows are popular and valued by the n8n community.\n- Discuss any trends, patterns, or feedback that highlight their significance.\n\n## Additional Insights\n- If available, provide extra information about the contributor's overall impact, such as their engagement in community forums or other notable contributions.\n\n## Formatting Guidelines\n- Use Markdown formatting exclusively (headers, lists, and tables) for clarity and organization.\n- Ensure your response is concise yet comprehensive, structured for easy navigation.\n\n## Error Handling\n- If data is unavailable or incomplete, clearly state this in your response and suggest possible reasons or next steps.\n\n## TOOLS\n\n### n8n_creator_stats \n- Use this tool to retrieve detailed statistics about the n8n creators.\n\n\n \n"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "f94de0ba-4d27-4b00-8f6c-b15ea2f37af7", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [-80, 280], "parameters": {"width": 320, "height": 340, "content": "## Telegram \n(Optional)\nhttps://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.telegram/"}, "typeVersion": 1}, {"id": "f50913c0-6615-4a5d-a4d4-2522280bc978", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-440, 720], "parameters": {"options": {"temperature": 0.2}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "L9UNQHflYlyF9Ngd", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "137b191e-9dae-4396-a536-dd77126ef176", "name": "Create Top 10 Workflows List", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-520, 380], "parameters": {"text": "=Create a list with hyperlinks of the top 10 workflows by weekly instertions from this report: {{ $json.output }}\n\nDo not include any preamble or further explanation. ", "promptType": "define"}, "typeVersion": 1.5}, {"id": "6249b1e5-2f47-469a-8bcc-16f41ee1da12", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-660, 280], "parameters": {"color": 5, "width": 540, "height": 700, "content": "## Create Top 10 Workflows List\n"}, "typeVersion": 1}, {"id": "9564db34-8b19-474e-812c-8a9d2cd028cb", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-540, 600], "parameters": {"color": 7, "width": 300, "height": 280, "content": "## Google Gemini LLM\nhttps://aistudio.google.com/apikey"}, "typeVersion": 1}, {"id": "065624e9-7f45-4607-94e9-2bf5a4f983ef", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [-80, 680], "parameters": {"color": 4, "width": 520, "height": 300, "content": "## Email Top 10 Workflows List\nhttps://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gmail/"}, "typeVersion": 1}, {"id": "532c071f-3ae0-4afd-9569-2ecc2ccebb02", "name": "Convert Top 10 Markdown to HTML", "type": "n8n-nodes-base.markdown", "position": [20, 780], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "={{ $json.text }}"}, "typeVersion": 1}, {"id": "f3aa0206-4449-41b1-aa4e-1fec6c948250", "name": "Gmail Creators & Workflows Report", "type": "n8n-nodes-base.gmail", "position": [-940, 780], "webhookId": "2bad33f7-38f8-40ca-9bcd-2f51179c8db5", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.data }}", "options": {}, "subject": "n8n Creator Stats"}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "2521435a-ad6e-4724-a07c-7762860b3f55", "name": "Telegram Top 10 Workflows List", "type": "n8n-nodes-base.telegram", "onError": "continueRegularOutput", "position": [20, 420], "webhookId": "8406b3d2-5ac6-452d-847f-c0886c8cd058", "parameters": {"text": "=n8n Creators Report - Top 10 Workflows\n{{ $now }}\n----------------------------------------------------\n{{ $json.text }}", "chatId": "={{ $env.TELEGRAM_CHAT_ID }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "pAIFhguJlkO3c7aQ", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "f234a3c1-18ba-488e-a88d-4a05be9eb9f4", "name": "Gmail Top 10 Workflows List", "type": "n8n-nodes-base.gmail", "position": [220, 780], "webhookId": "2bad33f7-38f8-40ca-9bcd-2f51179c8db5", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.data }}", "options": {}, "subject": "n8n Top 10 Workflows"}, "credentials": {"gmailOAuth2": {"id": "1xpVDEQ1yx8gV022", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "1267b550-5c8a-4fa3-8f0a-4d18f16a57c4", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [-2640, 580], "parameters": {"width": 540, "height": 900, "content": "# n8n Top Creators Leaderboard Reporting Workflow\n\n## Why This Workflow is Important\nThis workflow is a powerful tool for reporting on the n8n community's creators and workflows. It provides valuable insights into the most popular workflows, top contributors, and community trends. By automating data aggregation, processing, and report generation, it saves time and effort while fostering collaboration and inspiration within the n8n ecosystem.\n\n### Key Benefits:\n- **Discover Trends**: Identify top workflows based on unique visitors and inserters.\n- **Recognize Contributors**: Highlight impactful creators driving community engagement.\n- **Save Time**: Automates the entire reporting process, from data retrieval to report creation.\n\n## How to Use It\n1. **Set Up Prerequisites**: Ensure your n8n instance is running, GitHub data files are accessible, Google Gmail/Drive and OpenAI credentials are configured and Google Gemini credentials are configured.\n\n2. **Trigger the Workflow**:\n - Schedule the workflow to run daily or as needed.\n\n3. **Review Reports**:\n - The workflow generates a detailed Markdown report with summaries, tables, and insights.\n - Reports are saved locally or shared via email, Google Drive, or Telegram.\n\n\nThis workflow is ideal for creators, community managers, and new users looking to explore or optimize workflows within the n8n platform.\n"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"timezone": "America/Vancouver", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "619db74b-3f91-4d3b-b85d-e7e6bb972aca", "connections": {"Aggregate": {"main": [[{"node": "Workflow Response", "type": "main", "index": 0}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "n8n Creators Stats Agent", "type": "ai_languageModel", "index": 0}]]}, "Creators Data": {"main": [[{"node": "Merge Creators & Workflows", "type": "main", "index": 0}]]}, "Workflow Tool": {"ai_tool": [[{"node": "n8n Creators Stats Agent", "type": "ai_tool", "index": 0}]]}, "Workflows Data": {"main": [[{"node": "Merge Creators & Workflows", "type": "main", "index": 1}]]}, "creator-summary": {"main": [[{"node": "Save creator-summary.md", "type": "main", "index": 0}]]}, "Global Variables": {"main": [[{"node": "stats_aggregate_creators", "type": "main", "index": 0}, {"node": "stats_aggregate_workflows", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "n8n Creators Stats Agent", "type": "main", "index": 0}]]}, "Split Out Creators": {"main": [[{"node": "Sort By Top Weekly Creator Inserts", "type": "main", "index": 0}]]}, "Parse Creators Data": {"main": [[{"node": "Split Out Creators", "type": "main", "index": 0}]]}, "Parse Workflow Data": {"main": [[{"node": "Split Out Workflows", "type": "main", "index": 0}]]}, "Split Out Workflows": {"main": [[{"node": "Sort By Top Weekly Workflow Inserts", "type": "main", "index": 0}]]}, "Take Top 10 Creators": {"main": [[{"node": "Creators Data", "type": "main", "index": 0}]]}, "Take Top 50 Workflows": {"main": [[{"node": "Workflows Data", "type": "main", "index": 0}]]}, "Convert Markdown to HTML": {"main": [[{"node": "Gmail Creators & Workflows Report", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Create Top 10 Workflows List", "type": "ai_languageModel", "index": 0}]]}, "n8n Creators Stats Agent": {"main": [[{"node": "creator-summary", "type": "main", "index": 0}, {"node": "Google Drive", "type": "main", "index": 0}, {"node": "Convert Markdown to HTML", "type": "main", "index": 0}, {"node": "Create Top 10 Workflows List", "type": "main", "index": 0}]]}, "stats_aggregate_creators": {"main": [[{"node": "Parse Creators Data", "type": "main", "index": 0}]]}, "stats_aggregate_workflows": {"main": [[{"node": "Parse Workflow Data", "type": "main", "index": 0}]]}, "Merge Creators & Workflows": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Create Top 10 Workflows List": {"main": [[{"node": "Convert Top 10 Markdown to HTML", "type": "main", "index": 0}, {"node": "Telegram Top 10 Workflows List", "type": "main", "index": 0}]]}, "Convert Top 10 Markdown to HTML": {"main": [[{"node": "Gmail Top 10 Workflows List", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Global Variables", "type": "main", "index": 0}]]}, "Sort By Top Weekly Creator Inserts": {"main": [[{"node": "Take Top 10 Creators", "type": "main", "index": 0}]]}, "Sort By Top Weekly Workflow Inserts": {"main": [[{"node": "Take Top 50 Workflows", "type": "main", "index": 0}]]}}}