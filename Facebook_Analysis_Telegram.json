{"name": "Facebook Posts Analysis + Telegram", "nodes": [{"id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-500, -200], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "typeVersion": 1.2}, {"id": "manual-trigger", "name": "Manual Test", "type": "n8n-nodes-base.manualTrigger", "position": [-500, -50], "parameters": {}, "typeVersion": 1}, {"id": "facebook-pages", "name": "Facebook Pages", "type": "n8n-nodes-base.facebookGraphApi", "position": [-200, -120], "parameters": {"resource": "page", "operation": "getPosts", "pageId": "YOUR_PAGE_ID", "returnAll": false, "limit": 50, "additionalFields": {"fields": "id,message,created_time,likes.summary(true),comments.summary(true),shares"}}, "typeVersion": 1}, {"id": "aggregate-posts", "name": "Aggregate Posts", "type": "n8n-nodes-base.aggregate", "position": [100, -120], "parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "message"}]}}, "typeVersion": 1}, {"id": "ai-analysis", "name": "AI Analysis", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [400, -120], "parameters": {"text": "=Analyse ces posts Facebook et fournis un rapport détaillé :\n\n**Tâches :**\n1. **Thèmes principaux** : Identifie les sujets les plus discutés\n2. **Sentiment général** : Analyse le ton (positif, négatif, neutre)\n3. **Engagement** : Commente les niveaux d'interaction\n4. **Tendances** : Identifie les patterns récurrents\n5. **Recommandations** : Suggère des améliorations pour l'engagement\n\n**Posts à analyser :**\n{{ $json.message.join('\\n\\n---\\n\\n') }}\n\n**Format de réponse :**\n- Utilise des emojis pour rendre le rapport plus lisible\n- Structure avec des titres clairs\n- Inclus des exemples spécifiques\n- Limite à 3000 caractères pour Telegram", "promptType": "define"}, "typeVersion": 1.5}, {"id": "gemini-model", "name": "Google Gemini", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [500, 50], "parameters": {"options": {"temperature": 0.3}, "modelName": "models/gemini-2.0-flash-lite-preview"}, "typeVersion": 1}, {"id": "format-telegram", "name": "Format for Telegram", "type": "n8n-nodes-base.function", "position": [700, -120], "parameters": {"functionCode": "// Formater le message pour Telegram\nconst analysis = items[0].json.text;\nconst currentDate = new Date().toLocaleDateString('fr-FR', {\n  weekday: 'long',\n  year: 'numeric',\n  month: 'long',\n  day: 'numeric',\n  hour: '2-digit',\n  minute: '2-digit'\n});\n\n// Limiter la taille pour Telegram (max 4096 caractères)\nconst maxLength = 3800;\nlet formattedMessage = `📊 **RAPPORT D'ANALYSE FACEBOOK**\\n\\n📅 ${currentDate}\\n\\n${analysis}`;\n\nif (formattedMessage.length > maxLength) {\n  formattedMessage = formattedMessage.substring(0, maxLength) + '...\\n\\n✂️ *Rapport tronqué pour Telegram*';\n}\n\nreturn [{\n  json: {\n    message: formattedMessage,\n    parse_mode: 'Markdown'\n  }\n}];"}, "typeVersion": 1}, {"id": "telegram-send", "name": "Send to Telegram", "type": "n8n-nodes-base.telegram", "position": [900, -120], "parameters": {"text": "={{ $json.message }}", "chatId": "YOUR_TELEGRAM_CHAT_ID", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "typeVersion": 1.2}], "connections": {"Schedule Trigger": {"main": [[{"node": "Facebook Pages", "type": "main", "index": 0}]]}, "Manual Test": {"main": [[{"node": "Facebook Pages", "type": "main", "index": 0}]]}, "Facebook Pages": {"main": [[{"node": "Aggregate Posts", "type": "main", "index": 0}]]}, "Aggregate Posts": {"main": [[{"node": "AI Analysis", "type": "main", "index": 0}]]}, "AI Analysis": {"main": [[{"node": "Format for Telegram", "type": "main", "index": 0}]]}, "Format for Telegram": {"main": [[{"node": "Send to Telegram", "type": "main", "index": 0}]]}, "Google Gemini": {"ai_languageModel": [[{"node": "AI Analysis", "type": "ai_languageModel", "index": 0}]]}}}