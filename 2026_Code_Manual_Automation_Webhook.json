{"id": "yPIST7l13huQEjY5", "meta": {"instanceId": "98bf0d6aef1dd8b7a752798121440fb171bf7686b95727fd617f43452393daa3", "templateCredsSetupCompleted": true}, "name": "Use XMLRPC via HttpRequest-node to post on Wordpress.com", "tags": [{"id": "uumvgGHY5e6zEL7V", "name": "Published Template", "createdAt": "2025-02-10T11:18:10.923Z", "updatedAt": "2025-02-10T11:18:10.923Z"}], "nodes": [{"id": "8a64ffca-804a-4793-a721-3cb670aec22f", "name": "Settings", "type": "n8n-nodes-base.set", "position": [-380, -700], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1be018c7-51fe-4ea2-967d-ce47a2e8795c", "name": "wordpressUrl", "type": "string", "value": "YOURBLOG.wordpress.com"}, {"id": "95377f4f-184b-46a7-94c7-b2313c314cb2", "name": "wordpressUsername", "type": "string", "value": "YourUserName"}, {"id": "fdc99dc6-d9b0-4d2f-b770-1d8b6b360cad", "name": "wordpressApplicationPassword", "type": "string", "value": "your 4app pass word"}, {"id": "5aee5eef-9ad2-4dfb-a63f-1b5228c47e31", "name": "contentTitle", "type": "string", "value": "This is a demo title"}, {"id": "2abf516c-2910-4cd0-89fe-119cd0e616c8", "name": "contentText", "type": "string", "value": "This is the main text."}]}}, "typeVersion": 3.4}, {"id": "157b9656-5d90-44f4-aa3c-1285cda698d8", "name": "ManualTrigger", "type": "n8n-nodes-base.manualTrigger", "position": [-580, -700], "parameters": {}, "typeVersion": 1}, {"id": "1d2f6916-e5bd-497b-9843-8bb5a48e9866", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-420, -820], "parameters": {"width": 180, "height": 360, "content": "## Settings"}, "typeVersion": 1}, {"id": "1306446a-f628-44ba-9ca5-751b634bd5dd", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [140, -820], "parameters": {"color": 5, "width": 720, "height": 360, "content": "## Response Handling"}, "typeVersion": 1}, {"id": "ec3006aa-34c8-4522-8c37-980f68f168b5", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-220, -820], "parameters": {"color": 3, "width": 340, "height": 360, "content": "## Request Sending"}, "typeVersion": 1}, {"id": "bc918075-bea5-4a27-90d9-874b0917a958", "name": "Success", "type": "n8n-nodes-base.noOp", "position": [660, -780], "parameters": {}, "typeVersion": 1}, {"id": "3ea541b7-080e-4694-b865-d7d04f69ea0c", "name": "Error", "type": "n8n-nodes-base.noOp", "position": [660, -620], "parameters": {}, "typeVersion": 1}, {"id": "457c0687-ac1d-49e2-b434-6e1de9acb3a3", "name": "PrepareXML", "type": "n8n-nodes-base.code", "notes": "(request payload, escaping)", "position": [-180, -700], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const input = $json; // If other nodes are in between: $('Settings').item.json;\n\nconst username = input.wordpressUsername;\nconst password = input.wordpressApplicationPassword;\nconst title = input.contentTitle;\nconst text = input.contentText;\n\nconst blogId = 0;\nconst published = 1; // 0 = draft, 1 = published\n\n\n// Helper function to escape XML special characters\nfunction escapeXml(unsafe) {\n  return unsafe.replace(/[<>&'\"]/g, (c) => {\n    switch (c) {\n      case '<': return '&lt;';\n      case '>': return '&gt;';\n      case '&': return '&amp;';\n      case '\\'': return '&apos;';\n      case '\"': return '&quot;';\n      default: return c;\n    }\n  });\n}\n\n// Your actual post text, which may contain characters needing escaping\nconst titleEscaped = escapeXml(title);\nconst textEscaped = escapeXml(text);\n\n// Build the XML payload\nconst xmlData = `<?xml version=\"1.0\"?>\n<methodCall>\n  <methodName>wp.newPost</methodName>\n  <params>\n    <param>\n      <value><string>${blogId}</string></value>\n    </param>\n    <param>\n      <value><string>${username}</string></value>\n    </param>\n    <param>\n      <value><string>${password}</string></value>\n    </param>\n    <param>\n      <value>\n        <struct>\n          <member>\n            <name>post_title</name>\n            <value><string>${titleEscaped}</string></value>\n          </member>\n          <member>\n            <name>post_content</name>\n            <value><string>${textEscaped}</string></value>\n          </member>\n        </struct>\n      </value>\n    </param>\n    <param>\n      <value><boolean>${published}</boolean></value>\n    </param>\n  </params>\n</methodCall>`;\n\n\n// Add a new field called 'myNewField' to the JSON of the item\n$input.item.json.xmlRequestBody = xmlData;\n\nreturn $input.item;"}, "notesInFlow": true, "typeVersion": 2}, {"id": "3f29f3ed-f7ae-475b-bce3-04d3eeeacee9", "name": "PostRequest", "type": "n8n-nodes-base.httpRequest", "position": [-20, -700], "parameters": {"url": "=https://{{ $('Settings').item.json.wordpressUrl }}/xmlrpc.php", "body": "={{ $json.xmlRequestBody }}", "method": "POST", "options": {}, "sendBody": true, "contentType": "raw", "sendHeaders": true, "rawContentType": "text/xml", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "text/xml"}]}}, "typeVersion": 4.2}, {"id": "5f320d9b-8aa9-4d13-83db-86acaf444e92", "name": "IsSuccessful", "type": "n8n-nodes-base.if", "position": [420, -700], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "815d85a1-8f91-4338-977f-503f02c53ea2", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.methodResponse.params.param.value }}", "rightValue": ""}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "3a37d19a-12d3-474b-840f-c09342eecca9", "name": "HandleResponse", "type": "n8n-nodes-base.xml", "position": [220, -700], "parameters": {"options": {}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "78f90dc5-6209-4db0-b6c6-9f2324488605", "connections": {"Settings": {"main": [[{"node": "PrepareXML", "type": "main", "index": 0}]]}, "PrepareXML": {"main": [[{"node": "PostRequest", "type": "main", "index": 0}]]}, "PostRequest": {"main": [[{"node": "HandleResponse", "type": "main", "index": 0}]]}, "IsSuccessful": {"main": [[{"node": "Success", "type": "main", "index": 0}], [{"node": "Error", "type": "main", "index": 0}]]}, "ManualTrigger": {"main": [[{"node": "Settings", "type": "main", "index": 0}]]}, "HandleResponse": {"main": [[{"node": "IsSuccessful", "type": "main", "index": 0}]]}}}