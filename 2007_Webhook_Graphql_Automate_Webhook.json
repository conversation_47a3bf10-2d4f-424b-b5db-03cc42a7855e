{"id": "x2VUvhqV1YTJCIN0", "meta": {"instanceId": "e2c978396c9c745cf0aaa9ed3abe4464dbcef93c5fe2df809b9e14440e628df6"}, "tags": [], "nodes": [{"id": "094b9011-a53d-4a50-b44d-ad229612bb06", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [560, 220], "parameters": {}, "typeVersion": 1}, {"id": "6d9eee1f-995f-4558-8f97-25636e20022c", "name": "Save campaign.liquid", "type": "n8n-nodes-base.httpRequest", "position": [800, -100], "parameters": {"url": "=https://{{ $('Set values here!').params[\"fields\"][\"values\"][0][\"stringValue\"] }}.myshopify.com/admin/api/2024-01/themes/{{ $('Set values here!').params[\"fields\"][\"values\"][1][\"stringValue\"] }}/assets.json", "method": "PUT", "options": {}, "jsonBody": "={\"asset\":\n  {\n    \"key\":\"snippets/{{ $('Set values here!').params[\"fields\"][\"values\"][2][\"stringValue\"] }}\",\n    \"value\":\"{{ $('Set values here!').params[\"fields\"][\"values\"][3][\"stringValue\"].replace(\"IMAGE\",$('Check').item.json[\"body\"][\"items\"][0][\"Campaign Image\"][0][\"visible_name\"]).replace(/\\\\/g, \"\\\\\\\\\").replace(/\"/g, '\\\\\"').replace(/\\n/g, \"\\\\n\") }}\"}}", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "Z98cM8akgh1jPtG7", "name": "Header Auth  <PERSON>"}, "shopifyAccessTokenApi": {"id": "WbxXaLMHozAgY3Rz", "name": "Shopify Access Token account"}}, "typeVersion": 4.1}, {"id": "fb3e9410-59ae-4d90-8bb3-1fd95f0e9a43", "name": "Upload Image", "type": "n8n-nodes-base.graphql", "position": [560, -100], "parameters": {"query": "mutation fileCreate($files: [FileCreateInput!]!) {\n  fileCreate(files: $files) {\n    files {\n      id\n    }\n  }\n}", "endpoint": "=https://{{ $('Set values here!').params[\"fields\"][\"values\"][0][\"stringValue\"] }}.myshopify.com/admin/api/2024-01/graphql.json", "variables": "={\n  \"files\": {\n    \"alt\": \"{{ $json.body.items[0].Name }}\",\n    \"contentType\": \"IMAGE\",\n\t\"filename\": \"{{ $json.body.items[0]['Campaign Image'][0].visible_name }}\",\n    \"originalSource\": \"{{ $json.body.items[0]['Campaign Image'][0].url }}\"\n  }\n}", "requestFormat": "json", "authentication": "headerAuth"}, "credentials": {"httpHeaderAuth": {"id": "Z98cM8akgh1jPtG7", "name": "Header Auth  <PERSON>"}}, "typeVersion": 1}, {"id": "29f970fe-da65-4b6f-bf0b-1cadbd80f51c", "name": "Set values here!", "type": "n8n-nodes-base.set", "position": [120, 60], "parameters": {"fields": {"values": [{"name": "Shopify Subdomain", "stringValue": "n8n-mautic-demo"}, {"name": "Theme ID", "stringValue": "125514514534"}, {"name": "Filename", "stringValue": "campaign.liquid"}, {"name": "Content", "stringValue": "<img src=\"{{ 'IMAGE' | file_img_url: 'grande'}}\">"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "0bd9327d-4bbd-4884-a9a6-21b0c5b4c3d3", "name": "Call from Baserow", "type": "n8n-nodes-base.webhook", "position": [-100, 60], "webhookId": "3041fdd6-4cb5-4286-9034-1337dddc3f45", "parameters": {"path": "3041fdd6-4cb5-4286-9034-1337dddc3f45", "options": {}, "httpMethod": "POST"}, "typeVersion": 1.1}, {"id": "6c9d35e8-0738-4d15-a0ff-40077e73d797", "name": "Check", "type": "n8n-nodes-base.if", "position": [320, 60], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "*************-4f32-876b-82722a1fab66", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{\nDateTime.fromISO($json[\"body\"][\"items\"][0][\"Last modified\"])\n    .diff(DateTime.fromISO($json[\"body\"][\"old_items\"][0][\"Last modified\"]),'minutes')\n    .toObject()\n    [\"minutes\"]\n}}", "rightValue": 0.1}, {"id": "5c0a176c-5ba9-4060-a4d2-b9207cf47092", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ $json.body.items[0].Active }}", "rightValue": ""}, {"id": "f764adc6-e7a1-4df7-861f-94b90a99f2d4", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json[\"body\"][\"items\"][0][\"Campaign Image\"] }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "f6c17549-4192-4f96-ad81-518c52bdcda7", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-540, -40], "parameters": {"color": 4, "width": 360.408084305475, "height": 315.5897364788551, "content": "## Shopify API\n\nThis workflow uses GraphQL calls to the Shopify Admin API. In order to get a better understanding for the queries and mutations please check the API Docs.\n\n\n[Shopify GraphQL API docs](https://shopify.dev/docs/api/admin-graphql)\n\nTo make it easy to build queries for the GraphQL API easy please check out the [GraphiQL App for the Admin API](https://shopify.dev/docs/apps/tools/graphiql-admin-api) from Shopify"}, "typeVersion": 1}, {"id": "22743217-0c89-4fd1-b22d-0e00d6ca6854", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [560, -300], "parameters": {"width": 331.1188177339898, "content": "## Shopify \nThe n8n Shopify node cannot upload images or theme assets so we need to make custom calls to the GraphQL and REST Api "}, "typeVersion": 1}, {"id": "ca9561aa-85e8-47ad-8bac-60fc3a94f94e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [80, -160], "parameters": {"color": 5, "width": 158.16682590559316, "content": "## Set values \nPlease edit this node and change the values for your own setup."}, "typeVersion": 1}], "active": true, "pinData": {"Call from Baserow": [{"json": {"body": {"items": [{"id": 1, "Name": "Campaigna", "order": "1.00000000000000000000", "Active": true, "Last modified": "2024-03-01T11:21:58.157987Z", "Campaign Image": [{"url": "https://br.m3tam3re.com/media/user_files/O5jM7aSUTYSBPQtxVHktkN4U7wlUoIJd_1af752d7847a230a853df92814639be35035229f2bb857b4ea870b64011cdde0.webp", "name": "O5jM7aSUTYSBPQtxVHktkN4U7wlUoIJd_1af752d7847a230a853df92814639be35035229f2bb857b4ea870b64011cdde0.webp", "size": 107358, "is_image": true, "mime_type": "", "thumbnails": {"tiny": {"url": "https://br.m3tam3re.com/media/thumbnails/tiny/O5jM7aSUTYSBPQtxVHktkN4U7wlUoIJd_1af752d7847a230a853df92814639be35035229f2bb857b4ea870b64011cdde0.webp", "width": null, "height": 21}, "small": {"url": "https://br.m3tam3re.com/media/thumbnails/small/O5jM7aSUTYSBPQtxVHktkN4U7wlUoIJd_1af752d7847a230a853df92814639be35035229f2bb857b4ea870b64011cdde0.webp", "width": 48, "height": 48}, "card_cover": {"url": "https://br.m3tam3re.com/media/thumbnails/card_cover/O5jM7aSUTYSBPQtxVHktkN4U7wlUoIJd_1af752d7847a230a853df92814639be35035229f2bb857b4ea870b64011cdde0.webp", "width": 300, "height": 160}}, "image_width": 1280, "uploaded_at": "2024-03-01T09:50:41.921452+00:00", "image_height": 720, "visible_name": "n8n-portainer.webp"}]}], "event_id": "dae85cec-94ce-4e6c-8091-fce28bdc4c6c", "table_id": 596, "old_items": [{"id": 1, "Name": "Campaignas", "order": "1.00000000000000000000", "Active": true, "Last modified": "2024-03-01T11:21:16.099694Z", "Campaign Image": [{"url": "https://br.m3tam3re.com/media/user_files/O5jM7aSUTYSBPQtxVHktkN4U7wlUoIJd_1af752d7847a230a853df92814639be35035229f2bb857b4ea870b64011cdde0.webp", "name": "O5jM7aSUTYSBPQtxVHktkN4U7wlUoIJd_1af752d7847a230a853df92814639be35035229f2bb857b4ea870b64011cdde0.webp", "size": 107358, "is_image": true, "mime_type": "", "thumbnails": {"tiny": {"url": "https://br.m3tam3re.com/media/thumbnails/tiny/O5jM7aSUTYSBPQtxVHktkN4U7wlUoIJd_1af752d7847a230a853df92814639be35035229f2bb857b4ea870b64011cdde0.webp", "width": null, "height": 21}, "small": {"url": "https://br.m3tam3re.com/media/thumbnails/small/O5jM7aSUTYSBPQtxVHktkN4U7wlUoIJd_1af752d7847a230a853df92814639be35035229f2bb857b4ea870b64011cdde0.webp", "width": 48, "height": 48}, "card_cover": {"url": "https://br.m3tam3re.com/media/thumbnails/card_cover/O5jM7aSUTYSBPQtxVHktkN4U7wlUoIJd_1af752d7847a230a853df92814639be35035229f2bb857b4ea870b64011cdde0.webp", "width": 300, "height": 160}}, "image_width": 1280, "uploaded_at": "2024-03-01T09:50:41.921452+00:00", "image_height": 720, "visible_name": "n8n-portainer.webp"}]}], "event_type": "rows.updated", "database_id": 112, "workspace_id": 108}, "query": {}, "params": {}, "headers": {"host": "n8n.m3tam3re.com", "accept": "*/*", "user-agent": "python-requests/2.31.0", "content-type": "application/json", "content-length": "2617", "accept-encoding": "gzip, deflate, br", "x-baserow-event": "rows.updated", "x-forwarded-for": "**************", "x-forwarded-host": "n8n.m3tam3re.com", "x-forwarded-proto": "https", "x-baserow-delivery": "dae85cec-94ce-4e6c-8091-fce28bdc4c6c"}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "c82b43c0-aa47-4086-b7ae-588ee12e5e24", "connections": {"Check": {"main": [[{"node": "Upload Image", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Upload Image": {"main": [[{"node": "Save campaign.liquid", "type": "main", "index": 0}]]}, "Set values here!": {"main": [[{"node": "Check", "type": "main", "index": 0}]]}, "Call from Baserow": {"main": [[{"node": "Set values here!", "type": "main", "index": 0}]]}}}