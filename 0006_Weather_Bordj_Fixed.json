{"name": "Weather Bord<PERSON>", "nodes": [{"name": "Plivo", "type": "n8n-nodes-base.plivo", "position": [1030, 400], "parameters": {"message": "=Hey! The temperature outside is {{$node[\"OpenWeatherMap\"].json[\"main\"][\"temp\"]}}°C."}, "typeVersion": 1}, {"name": "OpenWeatherMap", "type": "n8n-nodes-base.openWeatherMap", "position": [830, 400], "parameters": {"cityName": "Bordj <PERSON>,DZ"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [630, 400], "parameters": {"triggerTimes": {"item": [{"hour": 9}]}}, "typeVersion": 1}], "connections": {"Cron": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}, "OpenWeatherMap": {"main": [[{"node": "Plivo", "type": "main", "index": 0}]]}}}